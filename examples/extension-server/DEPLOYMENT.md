# Custom Backend Extension Server Deployment Guide

This guide explains how to deploy and configure the custom backend extension server for Envoy Gateway.

## Prerequisites

1. Kubernetes cluster (v1.21+)
2. Envoy Gateway installed and running
3. kubectl configured to access the cluster
4. Helm 3.x (for deployment)

## Deployment Steps

### 1. Build and Push the Extension Server Image

```bash
# Build the extension server
make build

# Build the Docker image
make image

# Tag and push to your registry (if using external registry)
docker tag extension-server:latest your-registry/extension-server:latest
docker push your-registry/extension-server:latest
```

### 2. Install the CustomBackend CRD

```bash
# Apply the CRD
kubectl apply -f charts/extension-server/crds/generated/example.extensions.io_custombackends.yaml

# Verify CRD installation
kubectl get crd custombackends.example.extensions.io
```

### 3. Deploy the Extension Server

```bash
# Deploy using Helm
helm install extension-server ./charts/extension-server -n envoy-gateway-system

# Or with custom values
helm install extension-server ./charts/extension-server \
  -n envoy-gateway-system \
  --set image.repository=your-registry/extension-server \
  --set image.tag=latest
```

### 4. Configure Envoy Gateway

Apply the Envoy Gateway configuration to enable the extension server:

```bash
kubectl apply -f examples/envoy-gateway-config.yaml
```

This configuration:
- Registers the CustomBackend CRD as a policy resource
- Enables the Route and Translation hooks
- Points to the extension server service
- Adds necessary RBAC permissions

### 5. Restart Envoy Gateway

After updating the configuration, restart Envoy Gateway to pick up the changes:

```bash
kubectl rollout restart deployment/envoy-gateway -n envoy-gateway-system
```

### 6. Verify the Deployment

Check that all components are running:

```bash
# Check extension server
kubectl get pods -n envoy-gateway-system -l app.kubernetes.io/name=extension-server

# Check Envoy Gateway
kubectl get pods -n envoy-gateway-system -l control-plane=envoy-gateway

# Check extension server logs
kubectl logs -n envoy-gateway-system -l app.kubernetes.io/name=extension-server

# Check Envoy Gateway logs
kubectl logs -n envoy-gateway-system -l control-plane=envoy-gateway
```

## Configuration Options

### Extension Server Values

Key configuration options in `values.yaml`:

```yaml
# Image configuration
image:
  repository: extension-server
  tag: latest
  pullPolicy: IfNotPresent

# RBAC
rbac:
  create: true

# Service account
serviceAccount:
  create: true
  name: ""

# Resources
resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 50m
    memory: 64Mi
```

### Envoy Gateway Configuration

Key configuration options for Envoy Gateway:

```yaml
extensionManager:
  policyResources:
  - group: example.extensions.io
    version: v1alpha1
    kind: CustomBackend
  hooks:
    xdsTranslator:
      post:
      - Route          # For route modification
      - Translation    # For cluster creation
  service:
    fqdn:
      hostname: extension-server.envoy-gateway-system.svc.cluster.local
      port: 5005
  failOpen: false  # Set to true for fail-open behavior
```

## Testing the Deployment

1. Apply the example custom backends:
   ```bash
   kubectl apply -f examples/custom-backend-s3.yaml
   ```

2. Apply the example HTTPRoutes:
   ```bash
   kubectl apply -f examples/httproute-with-custom-backend.yaml
   ```

3. Run the test script:
   ```bash
   cd examples
   ./test-custom-backends.sh
   ```

## Troubleshooting

### Extension Server Not Starting

1. Check the pod logs:
   ```bash
   kubectl logs -n envoy-gateway-system -l app.kubernetes.io/name=extension-server
   ```

2. Verify RBAC permissions:
   ```bash
   kubectl auth can-i get custombackends --as=system:serviceaccount:envoy-gateway-system:extension-server
   ```

### Envoy Gateway Not Calling Extension Server

1. Check Envoy Gateway configuration:
   ```bash
   kubectl get configmap envoy-gateway-config -n envoy-gateway-system -o yaml
   ```

2. Check Envoy Gateway logs for extension-related errors:
   ```bash
   kubectl logs -n envoy-gateway-system -l control-plane=envoy-gateway | grep -i extension
   ```

### Custom Backends Not Working

1. Verify CustomBackend resources are created:
   ```bash
   kubectl get custombackends -A
   ```

2. Check HTTPRoute status:
   ```bash
   kubectl describe httproute custom-backend-route
   ```

3. Check Envoy configuration:
   ```bash
   # Get Envoy admin interface
   kubectl port-forward -n envoy-gateway-system svc/envoy-gateway-proxy-service 19000:19000
   
   # Check clusters
   curl localhost:19000/clusters
   
   # Check routes
   curl localhost:19000/config_dump | jq '.configs[].dynamic_route_configs'
   ```

## Security Considerations

1. **RBAC**: The extension server has cluster-wide read access to CustomBackend resources. Ensure this is acceptable for your security model.

2. **Network Policies**: Consider implementing network policies to restrict communication between the extension server and other components.

3. **Image Security**: Scan the extension server image for vulnerabilities before deployment.

4. **Secrets Management**: For production deployments, use proper secret management for any credentials in CustomBackend resources.

## Monitoring and Observability

1. **Metrics**: The extension server exposes basic gRPC metrics on the service port.

2. **Logging**: Configure appropriate log levels for both the extension server and Envoy Gateway.

3. **Health Checks**: The extension server includes basic health check endpoints.

## Upgrading

To upgrade the extension server:

1. Build and push the new image
2. Update the Helm chart values
3. Run `helm upgrade extension-server ./charts/extension-server -n envoy-gateway-system`
4. Verify the deployment

## Uninstalling

To remove the extension server:

```bash
# Remove Helm deployment
helm uninstall extension-server -n envoy-gateway-system

# Remove CRDs (this will delete all CustomBackend resources)
kubectl delete crd custombackends.example.extensions.io

# Remove Envoy Gateway configuration
kubectl delete configmap envoy-gateway-config -n envoy-gateway-system

# Restart Envoy Gateway
kubectl rollout restart deployment/envoy-gateway -n envoy-gateway-system
```
