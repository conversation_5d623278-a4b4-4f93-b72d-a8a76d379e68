// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extensionserver

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"

	clusterv3 "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	endpointv3 "github.com/envoyproxy/go-control-plane/envoy/config/endpoint/v3"
	listenerv3 "github.com/envoyproxy/go-control-plane/envoy/config/listener/v3"
	routev3 "github.com/envoyproxy/go-control-plane/envoy/config/route/v3"
	bav3 "github.com/envoyproxy/go-control-plane/envoy/extensions/filters/http/basic_auth/v3"
	hcm "github.com/envoyproxy/go-control-plane/envoy/extensions/filters/network/http_connection_manager/v3"
	tlsv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/transport_sockets/tls/v3"
	"github.com/envoyproxy/go-control-plane/pkg/wellknown"
	"github.com/exampleorg/envoygateway-extension/api/v1alpha1"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/wrapperspb"

	pb "github.com/envoyproxy/gateway/proto/extension"
)

type Server struct {
	pb.UnimplementedEnvoyGatewayExtensionServer

	log *slog.Logger
}

func New(logger *slog.Logger) *Server {
	return &Server{
		log: logger,
	}
}

// PostHTTPListenerModify is called after Envoy Gateway is done generating a
// Listener xDS configuration and before that configuration is passed on to
// Envoy Proxy.
// This example adds Basic Authentication on the Listener level as an example.
// Note: This implementation is not secure, and should not be used to protect
// anything important.
func (s *Server) PostHTTPListenerModify(ctx context.Context, req *pb.PostHTTPListenerModifyRequest) (*pb.PostHTTPListenerModifyResponse, error) {
	s.log.Info("postHTTPListenerModify callback was invoked")
	// Collect all of the required username/password combinations from the
	// provided contexts that were attached to the gateway.
	passwords := NewHtpasswd()
	for _, ext := range req.PostListenerContext.ExtensionResources {
		var listenerContext v1alpha1.ListenerContextExample
		if err := json.Unmarshal(ext.GetUnstructuredBytes(), &listenerContext); err != nil {
			s.log.Error("failed to unmarshal the extension", slog.String("error", err.Error()))
			continue
		}
		s.log.Info("processing an extension context", slog.String("username", listenerContext.Spec.Username))
		passwords.AddUser(listenerContext.Spec.Username, listenerContext.Spec.Password)
	}

	// First, get the filter chains from the listener
	filterChains := req.Listener.GetFilterChains()
	defaultFC := req.Listener.DefaultFilterChain
	if defaultFC != nil {
		filterChains = append(filterChains, defaultFC)
	}
	// Go over all of the chains, and add the basic authentication http filter
	for _, currChain := range filterChains {
		httpConManager, hcmIndex, err := findHCM(currChain)
		if err != nil {
			s.log.Error("failed to find an HCM in the current chain", slog.Any("error", err))
			continue
		}
		// If a basic authentication filter already exists, update it. Otherwise, create it.
		basicAuth, baIndex, err := findBasicAuthFilter(httpConManager.HttpFilters)
		if err != nil {
			s.log.Error("failed to unmarshal the existing basicAuth filter", slog.Any("error", err))
			continue
		}
		if baIndex == -1 {
			// Create a new basic auth filter
			basicAuth = &bav3.BasicAuth{
				Users: &corev3.DataSource{
					Specifier: &corev3.DataSource_InlineString{
						InlineString: passwords.String(),
					},
				},
				ForwardUsernameHeader: "X-Example-Ext",
			}
		} else {
			// Update the basic auth filter
			basicAuth.Users.Specifier = &corev3.DataSource_InlineString{
				InlineString: passwords.String(),
			}
		}
		// Add or update the Basic Authentication filter in the HCM
		anyBAFilter, _ := anypb.New(basicAuth)
		if baIndex > -1 {
			httpConManager.HttpFilters[baIndex].ConfigType = &hcm.HttpFilter_TypedConfig{
				TypedConfig: anyBAFilter,
			}
		} else {
			filters := []*hcm.HttpFilter{
				{
					Name: "envoy.filters.http.basic_auth",
					ConfigType: &hcm.HttpFilter_TypedConfig{
						TypedConfig: anyBAFilter,
					},
				},
			}
			filters = append(filters, httpConManager.HttpFilters...)
			httpConManager.HttpFilters = filters
		}

		// Write the updated HCM back to the filter chain
		anyConnectionMgr, _ := anypb.New(httpConManager)
		currChain.Filters[hcmIndex].ConfigType = &listenerv3.Filter_TypedConfig{
			TypedConfig: anyConnectionMgr,
		}
	}

	return &pb.PostHTTPListenerModifyResponse{
		Listener: req.Listener,
	}, nil
}

// Tries to find an HTTP connection manager in the provided filter chain.
func findHCM(filterChain *listenerv3.FilterChain) (*hcm.HttpConnectionManager, int, error) {
	for filterIndex, filter := range filterChain.Filters {
		if filter.Name == wellknown.HTTPConnectionManager {
			hcm := new(hcm.HttpConnectionManager)
			if err := filter.GetTypedConfig().UnmarshalTo(hcm); err != nil {
				return nil, -1, err
			}
			return hcm, filterIndex, nil
		}
	}
	return nil, -1, fmt.Errorf("unable to find HTTPConnectionManager in FilterChain: %s", filterChain.Name)
}

// Tries to find the Basic Authentication HTTP filter in the provided chain
func findBasicAuthFilter(chain []*hcm.HttpFilter) (*bav3.BasicAuth, int, error) {
	for i, filter := range chain {
		if filter.Name == "envoy.filters.http.basic_auth" {
			ba := new(bav3.BasicAuth)
			if err := filter.GetTypedConfig().UnmarshalTo(ba); err != nil {
				return nil, -1, err
			}
			return ba, i, nil
		}
	}
	return nil, -1, nil
}

// PostRouteModify is called after Envoy Gateway generates a Route configuration
// and before it is sent to Envoy Proxy. This hook allows us to modify routes
// that use custom backend references.
func (s *Server) PostRouteModify(ctx context.Context, req *pb.PostRouteModifyRequest) (*pb.PostRouteModifyResponse, error) {
	s.log.Info("PostRouteModify callback was invoked")

	// Check if this route uses a custom backend by examining extension resources
	customBackends := make(map[string]*v1alpha1.CustomBackend)
	for _, ext := range req.PostRouteContext.ExtensionResources {
		var customBackend v1alpha1.CustomBackend
		if err := json.Unmarshal(ext.GetUnstructuredBytes(), &customBackend); err != nil {
			s.log.Error("failed to unmarshal custom backend", slog.String("error", err.Error()))
			continue
		}
		s.log.Info("processing custom backend", slog.String("name", customBackend.Name), slog.String("type", customBackend.Spec.Type))
		customBackends[customBackend.Name] = &customBackend
	}

	// If no custom backends are found, return the route unchanged
	if len(customBackends) == 0 {
		return &pb.PostRouteModifyResponse{
			Route: req.Route,
		}, nil
	}

	// Modify the route to use custom cluster names
	route := req.Route
	if route.Action != nil {
		switch action := route.Action.(type) {
		case *routev3.Route_Route:
			if routeAction := action.Route; routeAction != nil {
				// Check if the current cluster name matches any custom backend
				if customBackend, exists := customBackends[routeAction.ClusterSpecifier.(*routev3.RouteAction_Cluster).Cluster]; exists {
					// Generate a custom cluster name based on the backend configuration
					customClusterName := generateCustomClusterName(customBackend)
					s.log.Info("modifying route cluster",
						slog.String("original", routeAction.ClusterSpecifier.(*routev3.RouteAction_Cluster).Cluster),
						slog.String("custom", customClusterName))

					// Update the cluster name to point to our custom cluster
					routeAction.ClusterSpecifier = &routev3.RouteAction_Cluster{
						Cluster: customClusterName,
					}
				}
			}
		}
	}

	return &pb.PostRouteModifyResponse{
		Route: route,
	}, nil
}

// generateCustomClusterName creates a unique cluster name for a custom backend
func generateCustomClusterName(backend *v1alpha1.CustomBackend) string {
	return fmt.Sprintf("custom-backend-%s-%s", backend.Spec.Type, backend.Name)
}

// PostTranslateModify is called after Envoy Gateway generates all clusters and secrets
// and before they are sent to Envoy Proxy. This hook allows us to inject custom clusters
// for our custom backends.
func (s *Server) PostTranslateModify(ctx context.Context, req *pb.PostTranslateModifyRequest) (*pb.PostTranslateModifyResponse, error) {
	s.log.Info("PostTranslateModify callback was invoked")

	// Parse custom backend resources from the extension context
	customBackends := make(map[string]*v1alpha1.CustomBackend)
	for _, ext := range req.PostTranslateContext.ExtensionResources {
		var customBackend v1alpha1.CustomBackend
		if err := json.Unmarshal(ext.GetUnstructuredBytes(), &customBackend); err != nil {
			s.log.Error("failed to unmarshal custom backend", slog.String("error", err.Error()))
			continue
		}
		s.log.Info("processing custom backend for cluster creation",
			slog.String("name", customBackend.Name),
			slog.String("type", customBackend.Spec.Type))
		customBackends[customBackend.Name] = &customBackend
	}

	// Start with the existing clusters
	clusters := req.Clusters
	secrets := req.Secrets

	// Create custom clusters for each custom backend
	for _, backend := range customBackends {
		customCluster := createCustomCluster(backend)
		if customCluster != nil {
			s.log.Info("adding custom cluster", slog.String("name", customCluster.Name))
			clusters = append(clusters, customCluster)
		}
	}

	return &pb.PostTranslateModifyResponse{
		Clusters: clusters,
		Secrets:  secrets,
	}, nil
}

// createCustomCluster creates an Envoy cluster configuration for a custom backend
func createCustomCluster(backend *v1alpha1.CustomBackend) *clusterv3.Cluster {
	clusterName := generateCustomClusterName(backend)

	// Create endpoints from the backend configuration
	var endpoints []*endpointv3.LbEndpoint
	for _, ep := range backend.Spec.Config.Endpoints {
		endpoint := &endpointv3.LbEndpoint{
			HostIdentifier: &endpointv3.LbEndpoint_Endpoint{
				Endpoint: &endpointv3.Endpoint{
					Address: &corev3.Address{
						Address: &corev3.Address_SocketAddress{
							SocketAddress: &corev3.SocketAddress{
								Protocol: corev3.SocketAddress_TCP,
								Address:  ep.Host,
								PortSpecifier: &corev3.SocketAddress_PortValue{
									PortValue: uint32(ep.Port),
								},
							},
						},
					},
				},
			},
		}

		// Set weight if specified
		if ep.Weight != nil {
			endpoint.LoadBalancingWeight = &wrapperspb.UInt32Value{
				Value: uint32(*ep.Weight),
			}
		}

		endpoints = append(endpoints, endpoint)
	}

	// Create the cluster
	cluster := &clusterv3.Cluster{
		Name: clusterName,
		ConnectTimeout: &durationpb.Duration{
			Seconds: 5, // Default 5 second timeout
		},
		ClusterDiscoveryType: &clusterv3.Cluster_Type{
			Type: clusterv3.Cluster_STATIC,
		},
		LoadAssignment: &endpointv3.ClusterLoadAssignment{
			ClusterName: clusterName,
			Endpoints: []*endpointv3.LocalityLbEndpoints{
				{
					LbEndpoints: endpoints,
				},
			},
		},
	}

	// Configure load balancing policy
	if backend.Spec.Config.LoadBalancing != nil && backend.Spec.Config.LoadBalancing.Policy != "" {
		switch strings.ToLower(backend.Spec.Config.LoadBalancing.Policy) {
		case "round_robin":
			cluster.LbPolicy = clusterv3.Cluster_ROUND_ROBIN
		case "least_request":
			cluster.LbPolicy = clusterv3.Cluster_LEAST_REQUEST
		case "random":
			cluster.LbPolicy = clusterv3.Cluster_RANDOM
		default:
			cluster.LbPolicy = clusterv3.Cluster_ROUND_ROBIN
		}
	} else {
		cluster.LbPolicy = clusterv3.Cluster_ROUND_ROBIN
	}

	// Configure TLS if enabled
	if backend.Spec.Config.TLS != nil && backend.Spec.Config.TLS.Enabled {
		tlsContext := &tlsv3.UpstreamTlsContext{}

		if backend.Spec.Config.TLS.ServerName != "" {
			tlsContext.Sni = backend.Spec.Config.TLS.ServerName
		}

		if backend.Spec.Config.TLS.InsecureSkipVerify {
			tlsContext.CommonTlsContext = &tlsv3.CommonTlsContext{
				ValidationContextType: &tlsv3.CommonTlsContext_ValidationContext{
					ValidationContext: &tlsv3.CertificateValidationContext{
						TrustedCa: &corev3.DataSource{
							Specifier: &corev3.DataSource_InlineString{
								InlineString: "", // Empty means skip verification
							},
						},
					},
				},
			}
		}

		transportSocket, _ := anypb.New(tlsContext)
		cluster.TransportSocket = &corev3.TransportSocket{
			Name: "envoy.transport_sockets.tls",
			ConfigType: &corev3.TransportSocket_TypedConfig{
				TypedConfig: transportSocket,
			},
		}
	}

	return cluster
}
