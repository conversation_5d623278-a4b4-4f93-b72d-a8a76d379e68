# Custom Backend Extension Server Examples

This directory contains examples demonstrating how to use the custom backend functionality in the Envoy Gateway Extension Server.

## Overview

The custom backend extension allows you to define and use custom backend types (such as S3, Lambda, EC2, etc.) that are not natively supported by Envoy Gateway. The extension server handles the translation of these custom backends into appropriate Envoy cluster configurations.

## Architecture

The custom backend functionality works through two main hooks:

1. **PostRouteModify Hook**: Detects routes that use custom backends and modifies the route configuration to point to custom cluster names.
2. **PostTranslateModify Hook**: Creates the actual Envoy cluster configurations for the custom backends.

## Files

- `custom-backend-s3.yaml`: Example CustomBackend resources for S3, Lambda, and external HTTP services
- `httproute-with-custom-backend.yaml`: Example HTTPRoutes that use custom backends
- `test-custom-backends.sh`: Test script to verify the functionality
- `README.md`: This documentation file

## CustomBackend Resource

The `CustomBackend` CRD allows you to define custom backend configurations:

```yaml
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: s3-backend
  namespace: default
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: eg
  type: s3
  config:
    endpoints:
    - host: s3.amazonaws.com
      port: 443
      weight: 100
    tls:
      enabled: true
      serverName: s3.amazonaws.com
    auth:
      type: aws-iam
      credentials:
        region: us-west-2
        accessKeyId: "AKIAIOSFODNN7EXAMPLE"
        secretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    healthCheck:
      enabled: true
      path: /health
      interval: 30s
      timeout: 5s
    loadBalancing:
      policy: round_robin
```

### Configuration Options

- **type**: The type of custom backend (e.g., "s3", "lambda", "ec2", "http")
- **endpoints**: List of backend endpoints with host, port, and optional weight
- **tls**: TLS configuration including server name and certificate verification settings
- **auth**: Authentication configuration with type and credentials
- **healthCheck**: Health check configuration for the backend
- **loadBalancing**: Load balancing policy (round_robin, least_request, random)

## Usage

### Method 1: Using ExtensionRef Filter

Use the custom backend as an extension filter in your HTTPRoute:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: custom-backend-route
spec:
  parentRefs:
  - name: eg
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /s3
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: s3-backend
    backendRefs:
    - name: s3-backend
      port: 443
```

### Method 2: Direct BackendRef (Future)

In the future, you may be able to use custom backends directly as backendRefs:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: direct-custom-backend-route
spec:
  parentRefs:
  - name: eg
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /direct-s3
    backendRefs:
    - group: example.extensions.io
      kind: CustomBackend
      name: s3-backend
      port: 443
```

## Testing

Run the test script to verify the functionality:

```bash
./test-custom-backends.sh
```

This script will:
1. Apply the CustomBackend CRD
2. Create example CustomBackend resources
3. Create example HTTPRoutes
4. Verify the resources are created correctly
5. Check extension server logs
6. Test connectivity (if possible)

## Supported Backend Types

The extension server can be configured to support various backend types:

- **s3**: Amazon S3 or S3-compatible storage
- **lambda**: AWS Lambda functions
- **ec2**: EC2 instances
- **http**: Generic HTTP/HTTPS services
- **custom**: Any custom backend type you define

## Prerequisites

1. Envoy Gateway installed and running
2. Extension server deployed with custom backend support
3. CustomBackend CRD installed
4. Proper RBAC permissions for the extension server

## Troubleshooting

1. **CRD not found**: Ensure the CustomBackend CRD is installed
2. **Extension server not responding**: Check extension server logs and ensure it's running
3. **Routes not working**: Verify the HTTPRoute configuration and check Envoy Gateway logs
4. **Backend connectivity issues**: Check the backend endpoint configuration and network connectivity

## Next Steps

- Implement authentication handling for different backend types
- Add support for more complex load balancing configurations
- Implement circuit breaker and retry policies
- Add metrics and observability for custom backends
