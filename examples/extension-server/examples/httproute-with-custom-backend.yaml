---
# Gateway configuration
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: eg
  namespace: default
spec:
  gatewayClassName: eg
  listeners:
  - name: http
    protocol: HTTP
    port: 80
---
# HTTPRoute using custom backend via extensionRef
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: custom-backend-route
  namespace: default
spec:
  parentRefs:
  - name: eg
  hostnames:
  - "api.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /s3
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: s3-backend
    backendRefs:
    - name: s3-backend  # This will be replaced by the extension
      port: 443
  - matches:
    - path:
        type: PathPrefix
        value: /lambda
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: lambda-backend
    backendRefs:
    - name: lambda-backend  # This will be replaced by the extension
      port: 443
  - matches:
    - path:
        type: PathPrefix
        value: /api
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: external-api-backend
    backendRefs:
    - name: external-api-backend  # This will be replaced by the extension
      port: 443
---
# Alternative approach: Using custom backend directly as backendRef
# Note: This requires Gateway API to support custom backend kinds
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: direct-custom-backend-route
  namespace: default
spec:
  parentRefs:
  - name: eg
  hostnames:
  - "direct.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /direct-s3
    backendRefs:
    - group: example.extensions.io
      kind: CustomBackend
      name: s3-backend
      port: 443
