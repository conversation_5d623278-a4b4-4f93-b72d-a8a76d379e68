---
# Envoy Gateway configuration with custom backend extension server
apiVersion: v1
kind: ConfigMap
metadata:
  name: envoy-gateway-config
  namespace: envoy-gateway-system
data:
  envoy-gateway.yaml: |
    apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: EnvoyGateway
    provider:
      type: Kubernetes
    gateway:
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    extensionManager:
      # Envoy Gateway will watch these resource kinds and use them as extension policies
      # which can be attached to Gateway resources.
      policyResources:
      - group: example.extensions.io
        version: v1alpha1
        kind: ListenerContextExample
      - group: example.extensions.io
        version: v1alpha1
        kind: CustomBackend
      hooks:
        # The type of hooks that should be invoked
        xdsTranslator:
          post:
          - HTTPListener    # For basic auth functionality
          - Route          # For custom backend route modification
          - Translation    # For custom backend cluster creation
      service:
        # The service that is hosting the extension server
        fqdn:
          hostname: extension-server.envoy-gateway-system.svc.cluster.local
          port: 5005
      # Optional: Configure fail-open behavior
      # failOpen: true
---
# RBAC permissions for Envoy Gateway to access custom backend resources
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: envoy-gateway-custom-backend-access
rules:
- apiGroups: ["example.extensions.io"]
  resources: ["custombackends"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["example.extensions.io"]
  resources: ["custombackends/status"]
  verbs: ["update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: envoy-gateway-custom-backend-access
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: envoy-gateway-custom-backend-access
subjects:
- kind: ServiceAccount
  name: envoy-gateway
  namespace: envoy-gateway-system
