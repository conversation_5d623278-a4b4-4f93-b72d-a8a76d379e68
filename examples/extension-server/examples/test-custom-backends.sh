#!/bin/bash

# Test script for custom backend functionality
set -e

echo "Testing Custom Backend Extension Server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if cluster is accessible
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot access Kubernetes cluster"
    exit 1
fi

print_status "Kubernetes cluster is accessible"

# Apply the CustomBackend CRD
print_status "Applying CustomBackend CRD..."
kubectl apply -f ../charts/extension-server/crds/generated/example.extensions.io_custombackends.yaml

# Wait for CRD to be established
print_status "Waiting for CRD to be established..."
kubectl wait --for condition=established --timeout=60s crd/custombackends.example.extensions.io

# Apply custom backend examples
print_status "Applying custom backend examples..."
kubectl apply -f custom-backend-s3.yaml

# Verify custom backends are created
print_status "Verifying custom backends..."
kubectl get custombackends -o wide

# Apply HTTPRoute examples
print_status "Applying HTTPRoute examples..."
kubectl apply -f httproute-with-custom-backend.yaml

# Verify HTTPRoutes are created
print_status "Verifying HTTPRoutes..."
kubectl get httproutes -o wide

# Check extension server logs (if running in cluster)
print_status "Checking extension server status..."
if kubectl get pods -n envoy-gateway-system -l app=extension-server &> /dev/null; then
    print_status "Extension server pods found:"
    kubectl get pods -n envoy-gateway-system -l app=extension-server
    
    print_status "Recent extension server logs:"
    kubectl logs -n envoy-gateway-system -l app=extension-server --tail=20
else
    print_warning "Extension server not found in cluster"
fi

# Check Envoy Gateway status
print_status "Checking Envoy Gateway status..."
if kubectl get pods -n envoy-gateway-system -l control-plane=envoy-gateway &> /dev/null; then
    kubectl get pods -n envoy-gateway-system -l control-plane=envoy-gateway
else
    print_warning "Envoy Gateway not found in cluster"
fi

# Test connectivity (if gateway is accessible)
print_status "Testing connectivity..."
GATEWAY_IP=$(kubectl get gateway eg -o jsonpath='{.status.addresses[0].value}' 2>/dev/null || echo "")

if [ -n "$GATEWAY_IP" ]; then
    print_status "Gateway IP: $GATEWAY_IP"
    
    # Test S3 endpoint
    print_status "Testing S3 endpoint..."
    if curl -s -H "Host: api.example.com" "http://$GATEWAY_IP/s3/test" -o /dev/null; then
        print_status "S3 endpoint is accessible"
    else
        print_warning "S3 endpoint test failed (this is expected if backend is not reachable)"
    fi
    
    # Test Lambda endpoint
    print_status "Testing Lambda endpoint..."
    if curl -s -H "Host: api.example.com" "http://$GATEWAY_IP/lambda/test" -o /dev/null; then
        print_status "Lambda endpoint is accessible"
    else
        print_warning "Lambda endpoint test failed (this is expected if backend is not reachable)"
    fi
    
    # Test external API endpoint
    print_status "Testing external API endpoint..."
    if curl -s -H "Host: api.example.com" "http://$GATEWAY_IP/api/test" -o /dev/null; then
        print_status "External API endpoint is accessible"
    else
        print_warning "External API endpoint test failed (this is expected if backend is not reachable)"
    fi
else
    print_warning "Gateway IP not available, skipping connectivity tests"
fi

print_status "Test completed successfully!"
print_status "Custom backend extension server is working correctly."

echo ""
print_status "To clean up, run:"
echo "kubectl delete -f httproute-with-custom-backend.yaml"
echo "kubectl delete -f custom-backend-s3.yaml"
echo "kubectl delete -f ../charts/extension-server/crds/generated/example.extensions.io_custombackends.yaml"
