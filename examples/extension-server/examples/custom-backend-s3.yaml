---
# Example CustomBackend for S3-like service
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: s3-backend
  namespace: default
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: eg
  type: s3
  config:
    endpoints:
    - host: s3.amazonaws.com
      port: 443
      weight: 100
    - host: s3-backup.amazonaws.com
      port: 443
      weight: 50
    tls:
      enabled: true
      serverName: s3.amazonaws.com
      insecureSkipVerify: false
    auth:
      type: aws-iam
      credentials:
        region: us-west-2
        accessKeyId: "AKIAIOSFODNN7EXAMPLE"
        secretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    healthCheck:
      enabled: true
      path: /health
      interval: 30s
      timeout: 5s
    loadBalancing:
      policy: round_robin
---
# Example CustomBackend for Lambda service
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: lambda-backend
  namespace: default
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: eg
  type: lambda
  config:
    endpoints:
    - host: lambda.us-west-2.amazonaws.com
      port: 443
    tls:
      enabled: true
      serverName: lambda.us-west-2.amazonaws.com
    auth:
      type: aws-iam
      credentials:
        region: us-west-2
        accessKeyId: "AKIAIOSFODNN7EXAMPLE"
        secretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    loadBalancing:
      policy: round_robin
---
# Example CustomBackend for external HTTP service
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: external-api-backend
  namespace: default
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: eg
  type: http
  config:
    endpoints:
    - host: api.external-service.com
      port: 443
    - host: api-backup.external-service.com
      port: 443
    tls:
      enabled: true
      serverName: api.external-service.com
    auth:
      type: bearer
      credentials:
        token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    healthCheck:
      enabled: true
      path: /health
      interval: 10s
      timeout: 3s
    loadBalancing:
      policy: least_request
