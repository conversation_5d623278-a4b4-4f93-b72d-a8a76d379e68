---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  name: custombackends.example.extensions.io
spec:
  group: example.extensions.io
  names:
    kind: CustomBackend
    listKind: CustomBackendList
    plural: custombackends
    singular: custombackend
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: CustomBackend provides a custom backend resource for extension
          server.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: CustomBackendSpec defines the desired state of CustomBackend
            properties:
              config:
                description: Config contains the backend-specific configuration
                properties:
                  auth:
                    description: Authentication configuration
                    properties:
                      credentials:
                        additionalProperties:
                          type: string
                        description: Credentials contains the authentication credentials
                        type: object
                      type:
                        description: Type of authentication (e.g., "basic", "bearer",
                          "aws-iam")
                        type: string
                    required:
                    - type
                    type: object
                  endpoints:
                    description: Endpoints specifies the backend endpoints
                    items:
                      description: CustomBackendEndpoint defines a backend endpoint
                      properties:
                        host:
                          description: Host is the hostname or IP address of the backend
                          type: string
                        port:
                          description: Port is the port number of the backend
                          format: int32
                          type: integer
                        weight:
                          description: Weight for load balancing (optional)
                          format: int32
                          type: integer
                      required:
                      - host
                      - port
                      type: object
                    type: array
                  healthCheck:
                    description: HealthCheck configuration
                    properties:
                      enabled:
                        description: Enabled indicates whether health check is enabled
                        type: boolean
                      interval:
                        description: Interval between health checks
                        type: string
                      path:
                        description: Path for HTTP health check
                        type: string
                      timeout:
                        description: Timeout for health check requests
                        type: string
                    required:
                    - enabled
                    type: object
                  loadBalancing:
                    description: LoadBalancing configuration
                    properties:
                      policy:
                        description: Policy specifies the load balancing policy (e.g.,
                          "round_robin", "least_request")
                        type: string
                    type: object
                  tls:
                    description: TLS configuration for the backend connection
                    properties:
                      enabled:
                        description: Enabled indicates whether TLS is enabled
                        type: boolean
                      insecureSkipVerify:
                        description: InsecureSkipVerify skips certificate verification
                        type: boolean
                      serverName:
                        description: ServerName for SNI
                        type: string
                    required:
                    - enabled
                    type: object
                required:
                - endpoints
                type: object
              targetRefs:
                description: TargetRefs identifies the Gateway resources this policy
                  should apply to.
                items:
                  description: |-
                    LocalPolicyTargetReferenceWithSectionName identifies an API object to apply a
                    direct policy to. This should be used as part of Policy resources that can
                    target single resources. For more information on how this policy attachment
                    mode works, and a sample Policy resource, refer to the policy attachment
                    documentation for Gateway API.

                    Note: This should only be used for direct policy attachment when references
                    to SectionName are actually needed. In all other cases,
                    LocalPolicyTargetReference should be used.
                  properties:
                    group:
                      description: Group is the group of the target resource.
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      description: Kind is kind of the target resource.
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    name:
                      description: Name is the name of the target resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    sectionName:
                      description: |-
                        SectionName is the name of a section within the target resource. When
                        unspecified, this targetRef targets the entire resource. In the following
                        resources, SectionName is interpreted as the following:

                        * Gateway: Listener name
                        * HTTPRoute: HTTPRouteRule name
                        * Service: Port name

                        If a SectionName is specified, but does not exist on the targeted object,
                        the Policy must fail to attach, and the policy implementation should record
                        a `ResolvedRefs` or similar Condition in the Policy's status.
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                  required:
                  - group
                  - kind
                  - name
                  type: object
                type: array
              type:
                description: Type specifies the type of custom backend (e.g., "s3",
                  "lambda", "ec2")
                type: string
            required:
            - config
            - targetRefs
            - type
            type: object
          status:
            description: CustomBackendStatus defines the observed state of CustomBackend
            properties:
              conditions:
                description: Conditions describe the current conditions of the CustomBackend
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
