{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "extension-server.fullname" . }}
  labels:
    {{- include "extension-server.labels" . | nindent 4 }}
rules:
# CustomBackend resources
- apiGroups: ["example.extensions.io"]
  resources: ["custombackends"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["example.extensions.io"]
  resources: ["custombackends/status"]
  verbs: ["update", "patch"]
# ListenerContextExample resources (existing)
- apiGroups: ["example.extensions.io"]
  resources: ["listenercontextexamples"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["example.extensions.io"]
  resources: ["listenercontextexamples/status"]
  verbs: ["update", "patch"]
# Gateway API resources (for reference resolution)
- apiGroups: ["gateway.networking.k8s.io"]
  resources: ["gateways", "httproutes", "grpcroutes"]
  verbs: ["get", "list", "watch"]
# Core resources
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "extension-server.fullname" . }}
  labels:
    {{- include "extension-server.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "extension-server.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "extension-server.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
