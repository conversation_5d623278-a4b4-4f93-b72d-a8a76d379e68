// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	gwapiv1a2 "sigs.k8s.io/gateway-api/apis/v1alpha2"
)

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
//
// ListenerContext provides an example extension policy context resource.
type ListenerContextExample struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec ListenerContextExampleSpec `json:"spec"`
}

type ListenerContextExampleSpec struct {
	TargetRefs []gwapiv1a2.LocalPolicyTargetReferenceWithSectionName `json:"targetRefs"`

	Username string `json:"username"`
	Password string `json:"password"`
}

// +kubebuilder:object:root=true
//
// ListenerContextList contains a list of ListenerContext resources.
type ListenerContextExampleList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ListenerContextExample `json:"items"`
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
//
// CustomBackend provides a custom backend resource for extension server.
type CustomBackend struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   CustomBackendSpec   `json:"spec"`
	Status CustomBackendStatus `json:"status,omitempty"`
}

// CustomBackendSpec defines the desired state of CustomBackend
type CustomBackendSpec struct {
	// TargetRefs identifies the Gateway resources this policy should apply to.
	TargetRefs []gwapiv1a2.LocalPolicyTargetReferenceWithSectionName `json:"targetRefs"`

	// Type specifies the type of custom backend (e.g., "s3", "lambda", "ec2")
	Type string `json:"type"`

	// Config contains the backend-specific configuration
	Config CustomBackendConfig `json:"config"`
}

// CustomBackendConfig contains the configuration for different backend types
type CustomBackendConfig struct {
	// Endpoints specifies the backend endpoints
	Endpoints []CustomBackendEndpoint `json:"endpoints"`

	// TLS configuration for the backend connection
	TLS *CustomBackendTLS `json:"tls,omitempty"`

	// Authentication configuration
	Auth *CustomBackendAuth `json:"auth,omitempty"`

	// HealthCheck configuration
	HealthCheck *CustomBackendHealthCheck `json:"healthCheck,omitempty"`

	// LoadBalancing configuration
	LoadBalancing *CustomBackendLoadBalancing `json:"loadBalancing,omitempty"`
}

// CustomBackendEndpoint defines a backend endpoint
type CustomBackendEndpoint struct {
	// Host is the hostname or IP address of the backend
	Host string `json:"host"`

	// Port is the port number of the backend
	Port int32 `json:"port"`

	// Weight for load balancing (optional)
	Weight *int32 `json:"weight,omitempty"`
}

// CustomBackendTLS defines TLS configuration for backend connections
type CustomBackendTLS struct {
	// Enabled indicates whether TLS is enabled
	Enabled bool `json:"enabled"`

	// ServerName for SNI
	ServerName string `json:"serverName,omitempty"`

	// InsecureSkipVerify skips certificate verification
	InsecureSkipVerify bool `json:"insecureSkipVerify,omitempty"`
}

// CustomBackendAuth defines authentication configuration
type CustomBackendAuth struct {
	// Type of authentication (e.g., "basic", "bearer", "aws-iam")
	Type string `json:"type"`

	// Credentials contains the authentication credentials
	Credentials map[string]string `json:"credentials,omitempty"`
}

// CustomBackendHealthCheck defines health check configuration
type CustomBackendHealthCheck struct {
	// Enabled indicates whether health check is enabled
	Enabled bool `json:"enabled"`

	// Path for HTTP health check
	Path string `json:"path,omitempty"`

	// Interval between health checks
	Interval string `json:"interval,omitempty"`

	// Timeout for health check requests
	Timeout string `json:"timeout,omitempty"`
}

// CustomBackendLoadBalancing defines load balancing configuration
type CustomBackendLoadBalancing struct {
	// Policy specifies the load balancing policy (e.g., "round_robin", "least_request")
	Policy string `json:"policy,omitempty"`
}

// CustomBackendStatus defines the observed state of CustomBackend
type CustomBackendStatus struct {
	// Conditions describe the current conditions of the CustomBackend
	Conditions []metav1.Condition `json:"conditions,omitempty"`
}

// +kubebuilder:object:root=true
//
// CustomBackendList contains a list of CustomBackend resources.
type CustomBackendList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []CustomBackend `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ListenerContextExample{}, &ListenerContextExampleList{})
	SchemeBuilder.Register(&CustomBackend{}, &CustomBackendList{})
}
