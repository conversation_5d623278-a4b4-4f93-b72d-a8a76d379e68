# Envoy Gateway Extension Server

This extension server demonstrates how to extend Envoy Gateway functionality using the extension server mechanism. It provides two main features:

1. **Basic Authentication** (original example)
2. **Custom Backend Support** (new feature)

## Features

### Basic Authentication

The extension server can add HTTP Basic Authentication to listeners managed by Envoy Gateway. This is controlled by the `ListenerContextExample` CRD.

### Custom Backend Support

The extension server supports custom backend types that are not natively supported by Envoy Gateway, such as:

- Amazon S3 or S3-compatible storage
- AWS Lambda functions
- EC2 instances
- External HTTP/HTTPS APIs
- Any custom backend type you define

This functionality is controlled by the `CustomBackend` CRD and works through two extension hooks:

- **PostRouteModify**: Modifies routes to use custom cluster names
- **PostTranslateModify**: Creates Envoy cluster configurations for custom backends

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTPRoute     │    │  Extension       │    │  Custom         │
│   with Custom   │───▶│  Server          │───▶│  Backend        │
│   BackendRef    │    │                  │    │  Cluster        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  Envoy Proxy     │
                       │  Configuration   │
                       └──────────────────┘
```

## Quick Start

### Prerequisites

- Kubernetes cluster
- Envoy Gateway installed
- kubectl and Helm 3.x

### 1. Deploy the Extension Server

```bash
# Build and deploy
make build image
helm install extension-server ./charts/extension-server -n envoy-gateway-system
```

### 2. Configure Envoy Gateway

```bash
kubectl apply -f examples/envoy-gateway-config.yaml
kubectl rollout restart deployment/envoy-gateway -n envoy-gateway-system
```

### 3. Create Custom Backends

```bash
kubectl apply -f examples/custom-backend-s3.yaml
```

### 4. Create HTTPRoutes

```bash
kubectl apply -f examples/httproute-with-custom-backend.yaml
```

### 5. Test the Setup

```bash
cd examples
./test-custom-backends.sh
```

## Custom Backend Configuration

Define custom backends using the `CustomBackend` CRD:

```yaml
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: s3-backend
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: eg
  type: s3
  config:
    endpoints:
    - host: s3.amazonaws.com
      port: 443
    tls:
      enabled: true
      serverName: s3.amazonaws.com
    auth:
      type: aws-iam
      credentials:
        region: us-west-2
        accessKeyId: "AKIAIOSFODNN7EXAMPLE"
        secretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    loadBalancing:
      policy: round_robin
```

## Usage in HTTPRoutes

Use custom backends in your HTTPRoutes via extension filters:

```yaml
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: custom-backend-route
spec:
  parentRefs:
  - name: eg
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /s3
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: s3-backend
    backendRefs:
    - name: s3-backend
      port: 443
```

## Supported Backend Types

- **s3**: Amazon S3 or S3-compatible storage
- **lambda**: AWS Lambda functions
- **ec2**: EC2 instances
- **http**: Generic HTTP/HTTPS services
- **custom**: Any custom backend type

## Configuration Options

### Endpoints
- Multiple endpoints with optional weights
- Host and port configuration
- Load balancing across endpoints

### TLS Configuration
- Enable/disable TLS
- Server name for SNI
- Certificate verification options

### Authentication
- Multiple auth types: aws-iam, bearer, basic
- Credential management
- Per-backend auth configuration

### Health Checks
- HTTP health check configuration
- Configurable intervals and timeouts
- Path-based health checks

### Load Balancing
- Round robin, least request, random policies
- Weighted endpoint distribution
- Zone-aware routing support

## Development

### Building

```bash
# Generate code
make generate

# Generate manifests
make manifests

# Build binary
make build

# Build Docker image
make image
```

### Testing

```bash
# Run unit tests
go test ./...

# Run integration tests
cd examples
./test-custom-backends.sh
```

## Documentation

- [Deployment Guide](DEPLOYMENT.md) - Detailed deployment instructions
- [Examples](examples/README.md) - Usage examples and configurations
- [API Reference](api/v1alpha1/) - CRD specifications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## Support

For questions and support:

- Create an issue in the repository
- Join the Envoy Gateway community discussions
- Check the troubleshooting section in the deployment guide
