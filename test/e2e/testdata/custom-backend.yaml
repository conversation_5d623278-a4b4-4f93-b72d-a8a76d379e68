---
# CustomBackend CRD (this would normally be installed separately)
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: custombackends.example.extensions.io
spec:
  group: example.extensions.io
  versions:
  - name: v1alpha1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              targetRefs:
                type: array
                items:
                  type: object
              type:
                type: string
              config:
                type: object
          status:
            type: object
  scope: Namespaced
  names:
    plural: custombackends
    singular: custombackend
    kind: CustomBackend
---
# S3 Custom Backend
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: s3-backend
  namespace: gateway-conformance-infra
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: same-namespace
  type: s3
  config:
    endpoints:
    - host: s3.amazonaws.com
      port: 443
      weight: 100
    tls:
      enabled: true
      serverName: s3.amazonaws.com
      insecureSkipVerify: false
    auth:
      type: aws-iam
      credentials:
        region: us-west-2
        accessKeyId: "AKIAIOSFODNN7EXAMPLE"
        secretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    healthCheck:
      enabled: true
      path: /health
      interval: 30s
      timeout: 5s
    loadBalancing:
      policy: round_robin
---
# Lambda Custom Backend
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: lambda-backend
  namespace: gateway-conformance-infra
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: same-namespace
  type: lambda
  config:
    endpoints:
    - host: lambda.us-west-2.amazonaws.com
      port: 443
    tls:
      enabled: true
      serverName: lambda.us-west-2.amazonaws.com
    auth:
      type: aws-iam
      credentials:
        region: us-west-2
        accessKeyId: "AKIAIOSFODNN7EXAMPLE"
        secretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    loadBalancing:
      policy: round_robin
---
# HTTPRoute using S3 custom backend
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: custom-backend-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  hostnames:
  - "s3.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /s3
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: s3-backend
    backendRefs:
    - group: example.extensions.io
      kind: CustomBackend
      name: s3-backend
      port: 443
---
# HTTPRoute using Lambda custom backend
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: lambda-backend-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  hostnames:
  - "lambda.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /lambda
    filters:
    - type: ExtensionRef
      extensionRef:
        group: example.extensions.io
        kind: CustomBackend
        name: lambda-backend
    backendRefs:
    - group: example.extensions.io
      kind: CustomBackend
      name: lambda-backend
      port: 443
---
# External API Custom Backend
apiVersion: example.extensions.io/v1alpha1
kind: CustomBackend
metadata:
  name: external-api-backend
  namespace: gateway-conformance-infra
spec:
  targetRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: same-namespace
  type: http
  config:
    endpoints:
    - host: api.external-service.com
      port: 443
    - host: api-backup.external-service.com
      port: 443
    tls:
      enabled: true
      serverName: api.external-service.com
    auth:
      type: bearer
      credentials:
        token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    healthCheck:
      enabled: true
      path: /health
      interval: 10s
      timeout: 3s
    loadBalancing:
      policy: least_request
---
# HTTPRoute using external API custom backend
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: external-api-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  hostnames:
  - "api.example.com"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /api
    backendRefs:
    - group: example.extensions.io
      kind: CustomBackend
      name: external-api-backend
      port: 443
