// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

//go:build e2e
// +build e2e

package tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/utils/ptr"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/envoyproxy/gateway/test/e2e/suite"
)

func init() {
	ConformanceTests = append(ConformanceTests, CustomBackendTest)
}

var CustomBackendTest = suite.ConformanceTest{
	ShortName:   "CustomBackend",
	Description: "Test custom backend functionality with extension server",
	Manifests:   []string{"testdata/custom-backend.yaml"},
	Test: func(t *testing.T, suite *suite.ConformanceTestSuite) {
		t.Run("custom backend with S3", func(t *testing.T) {
			ns := "gateway-conformance-infra"
			routeName := "custom-backend-route"
			gwName := "same-namespace"

			ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
			defer cancel()

			// Check that the HTTPRoute was accepted
			route := &gwapiv1.HTTPRoute{}
			err := suite.Client.Get(ctx, types.NamespacedName{
				Namespace: ns,
				Name:      routeName,
			}, route)
			require.NoError(t, err)

			// Verify the route status shows it's accepted
			require.Eventually(t, func() bool {
				err := suite.Client.Get(ctx, types.NamespacedName{
					Namespace: ns,
					Name:      routeName,
				}, route)
				if err != nil {
					return false
				}

				for _, parent := range route.Status.Parents {
					if string(parent.ParentRef.Name) == gwName {
						for _, condition := range parent.Conditions {
							if condition.Type == string(gwapiv1.RouteConditionAccepted) &&
								condition.Status == "True" {
								return true
							}
						}
					}
				}
				return false
			}, time.Minute, time.Second)

			// Check that the CustomBackend resource exists
			customBackend := &unstructured.Unstructured{}
			customBackend.SetAPIVersion("example.extensions.io/v1alpha1")
			customBackend.SetKind("CustomBackend")
			
			err = suite.Client.Get(ctx, types.NamespacedName{
				Namespace: ns,
				Name:      "s3-backend",
			}, customBackend)
			require.NoError(t, err)

			// Verify the custom backend has the expected configuration
			spec, found, err := unstructured.NestedMap(customBackend.Object, "spec")
			require.NoError(t, err)
			require.True(t, found)

			backendType, found, err := unstructured.NestedString(spec, "type")
			require.NoError(t, err)
			require.True(t, found)
			require.Equal(t, "s3", backendType)

			// Check that the Gateway is ready
			gw := &gwapiv1.Gateway{}
			err = suite.Client.Get(ctx, types.NamespacedName{
				Namespace: ns,
				Name:      gwName,
			}, gw)
			require.NoError(t, err)

			require.Eventually(t, func() bool {
				err := suite.Client.Get(ctx, types.NamespacedName{
					Namespace: ns,
					Name:      gwName,
				}, gw)
				if err != nil {
					return false
				}

				for _, condition := range gw.Status.Conditions {
					if condition.Type == string(gwapiv1.GatewayConditionProgrammed) &&
						condition.Status == "True" {
						return true
					}
				}
				return false
			}, time.Minute, time.Second)
		})

		t.Run("custom backend with Lambda", func(t *testing.T) {
			ns := "gateway-conformance-infra"
			routeName := "lambda-backend-route"

			ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
			defer cancel()

			// Check that the Lambda CustomBackend resource exists
			lambdaBackend := &unstructured.Unstructured{}
			lambdaBackend.SetAPIVersion("example.extensions.io/v1alpha1")
			lambdaBackend.SetKind("CustomBackend")
			
			err := suite.Client.Get(ctx, types.NamespacedName{
				Namespace: ns,
				Name:      "lambda-backend",
			}, lambdaBackend)
			require.NoError(t, err)

			// Verify the lambda backend configuration
			spec, found, err := unstructured.NestedMap(lambdaBackend.Object, "spec")
			require.NoError(t, err)
			require.True(t, found)

			backendType, found, err := unstructured.NestedString(spec, "type")
			require.NoError(t, err)
			require.True(t, found)
			require.Equal(t, "lambda", backendType)

			// Check that the HTTPRoute using Lambda backend was accepted
			route := &gwapiv1.HTTPRoute{}
			err = suite.Client.Get(ctx, types.NamespacedName{
				Namespace: ns,
				Name:      routeName,
			}, route)
			require.NoError(t, err)

			require.Eventually(t, func() bool {
				err := suite.Client.Get(ctx, types.NamespacedName{
					Namespace: ns,
					Name:      routeName,
				}, route)
				if err != nil {
					return false
				}

				for _, parent := range route.Status.Parents {
					for _, condition := range parent.Conditions {
						if condition.Type == string(gwapiv1.RouteConditionAccepted) &&
							condition.Status == "True" {
							return true
						}
					}
				}
				return false
			}, time.Minute, time.Second)
		})

		t.Run("invalid custom backend should be rejected", func(t *testing.T) {
			ns := "gateway-conformance-infra"
			
			ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
			defer cancel()

			// Create an HTTPRoute with an invalid custom backend reference
			invalidRoute := &gwapiv1.HTTPRoute{
				ObjectMeta: suite.TimeoutConfig.GetObjectMeta("invalid-backend-route", ns),
				Spec: gwapiv1.HTTPRouteSpec{
					ParentRefs: []gwapiv1.ParentReference{{
						Name: "same-namespace",
					}},
					Hostnames: []gwapiv1.Hostname{"invalid.example.com"},
					Rules: []gwapiv1.HTTPRouteRule{{
						Matches: []gwapiv1.HTTPRouteMatch{{
							Path: &gwapiv1.HTTPPathMatch{
								Type:  ptr.To(gwapiv1.PathMatchPathPrefix),
								Value: ptr.To("/invalid"),
							},
						}},
						BackendRefs: []gwapiv1.HTTPBackendRef{{
							BackendRef: gwapiv1.BackendRef{
								BackendObjectReference: gwapiv1.BackendObjectReference{
									Group: ptr.To(gwapiv1.Group("unknown.extensions.io")),
									Kind:  ptr.To(gwapiv1.Kind("UnknownBackend")),
									Name:  "unknown-backend",
									Port:  ptr.To(gwapiv1.PortNumber(443)),
								},
							},
						}},
					}},
				},
			}

			err := suite.Client.Create(ctx, invalidRoute)
			require.NoError(t, err)

			// Clean up
			defer func() {
				_ = suite.Client.Delete(ctx, invalidRoute)
			}()

			// The route should be rejected due to invalid backend kind
			require.Eventually(t, func() bool {
				err := suite.Client.Get(ctx, types.NamespacedName{
					Namespace: ns,
					Name:      "invalid-backend-route",
				}, invalidRoute)
				if err != nil {
					return false
				}

				for _, parent := range invalidRoute.Status.Parents {
					for _, condition := range parent.Conditions {
						if condition.Type == string(gwapiv1.RouteConditionAccepted) &&
							condition.Status == "False" &&
							condition.Reason == string(gwapiv1.RouteReasonInvalidKind) {
							return true
						}
					}
				}
				return false
			}, time.Minute, time.Second)
		})
	},
}
