apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  creationTimestamp: null
  name: custom-pdb-name
  namespace: envoy-gateway-system
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: GatewayClass
    name: envoy-gateway-class
    uid: test-owner-reference-uid-for-gatewayclass
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: proxy
      app.kubernetes.io/managed-by: envoy-gateway
      app.kubernetes.io/name: envoy
      gateway.envoyproxy.io/owning-gateway-name: default
      gateway.envoyproxy.io/owning-gateway-namespace: default
status:
  currentHealthy: 0
  desiredHealthy: 0
  disruptionsAllowed: 0
  expectedPods: 0
