// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package gatewayapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/utils/ptr"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
	gwapiv1a2 "sigs.k8s.io/gateway-api/apis/v1alpha2"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/ir"
)

func TestCustomBackendValidation(t *testing.T) {
	translator := &Translator{
		GatewayControllerName: egv1a1.GatewayControllerName,
		ExtensionGroupKinds: []egv1a1.GroupVersionKind{
			{
				Group:   "example.extensions.io",
				Version: "v1alpha1",
				Kind:    "CustomBackend",
			},
		},
	}

	tests := []struct {
		name        string
		backendRef  *gwapiv1a2.BackendRef
		expectError bool
	}{
		{
			name: "valid service backend",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: ptr.To(gwapiv1.Kind("Service")),
					Name: "test-service",
					Port: ptr.To(gwapiv1.PortNumber(80)),
				},
			},
			expectError: false,
		},
		{
			name: "valid custom backend",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Group: ptr.To(gwapiv1.Group("example.extensions.io")),
					Kind:  ptr.To(gwapiv1.Kind("CustomBackend")),
					Name:  "s3-backend",
					Port:  ptr.To(gwapiv1.PortNumber(443)),
				},
			},
			expectError: false,
		},
		{
			name: "invalid custom backend - unknown group",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Group: ptr.To(gwapiv1.Group("unknown.extensions.io")),
					Kind:  ptr.To(gwapiv1.Kind("CustomBackend")),
					Name:  "s3-backend",
					Port:  ptr.To(gwapiv1.PortNumber(443)),
				},
			},
			expectError: true,
		},
		{
			name: "invalid custom backend - unknown kind",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Group: ptr.To(gwapiv1.Group("example.extensions.io")),
					Kind:  ptr.To(gwapiv1.Kind("UnknownBackend")),
					Name:  "s3-backend",
					Port:  ptr.To(gwapiv1.PortNumber(443)),
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := translator.validateBackendRefKind(tt.backendRef)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCustomBackendDestinationSetting(t *testing.T) {
	translator := &Translator{
		GatewayControllerName: egv1a1.GatewayControllerName,
		ExtensionGroupKinds: []egv1a1.GroupVersionKind{
			{
				Group:   "example.extensions.io",
				Version: "v1alpha1",
				Kind:    "CustomBackend",
			},
		},
	}

	// Create a custom backend resource
	customBackend := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "example.extensions.io/v1alpha1",
			"kind":       "CustomBackend",
			"metadata": map[string]interface{}{
				"name":      "s3-backend",
				"namespace": "default",
			},
			"spec": map[string]interface{}{
				"type": "s3",
				"config": map[string]interface{}{
					"endpoints": []interface{}{
						map[string]interface{}{
							"host": "s3.amazonaws.com",
							"port": 443,
						},
					},
					"tls": map[string]interface{}{
						"enabled":    true,
						"serverName": "s3.amazonaws.com",
					},
				},
			},
		},
	}

	resources := &resource.Resources{
		ExtensionServerPolicies: []unstructured.Unstructured{*customBackend},
	}

	backendRef := gwapiv1.BackendObjectReference{
		Group: ptr.To(gwapiv1.Group("example.extensions.io")),
		Kind:  ptr.To(gwapiv1.Kind("CustomBackend")),
		Name:  "s3-backend",
		Port:  ptr.To(gwapiv1.PortNumber(443)),
	}

	ds := translator.processExtensionBackendDestinationSetting(
		"test-destination",
		backendRef,
		"default",
		ir.HTTP,
		resources,
	)

	require.NotNil(t, ds)
	assert.Equal(t, "test-destination", ds.Name)
	assert.Equal(t, ir.HTTP, ds.Protocol)
	assert.True(t, ds.ExtensionBackend)
	assert.Len(t, ds.Endpoints, 1)
	assert.Equal(t, "extension-backend-placeholder", ds.Endpoints[0].Host)
	assert.Equal(t, uint32(80), ds.Endpoints[0].Port)
	assert.NotNil(t, ds.Metadata)
	assert.Equal(t, "s3-backend", ds.Metadata.Name)
	assert.Equal(t, "default", ds.Metadata.Namespace)
}

func TestIsExtensionBackendKind(t *testing.T) {
	translator := &Translator{
		ExtensionGroupKinds: []egv1a1.GroupVersionKind{
			{
				Group:   "example.extensions.io",
				Version: "v1alpha1",
				Kind:    "CustomBackend",
			},
			{
				Group:   "aws.extensions.io",
				Version: "v1beta1",
				Kind:    "S3Backend",
			},
		},
	}

	tests := []struct {
		name     string
		group    *gwapiv1.Group
		kind     gwapiv1.Kind
		expected bool
	}{
		{
			name:     "valid custom backend",
			group:    ptr.To(gwapiv1.Group("example.extensions.io")),
			kind:     "CustomBackend",
			expected: true,
		},
		{
			name:     "valid S3 backend",
			group:    ptr.To(gwapiv1.Group("aws.extensions.io")),
			kind:     "S3Backend",
			expected: true,
		},
		{
			name:     "invalid group",
			group:    ptr.To(gwapiv1.Group("unknown.extensions.io")),
			kind:     "CustomBackend",
			expected: false,
		},
		{
			name:     "invalid kind",
			group:    ptr.To(gwapiv1.Group("example.extensions.io")),
			kind:     "UnknownBackend",
			expected: false,
		},
		{
			name:     "nil group",
			group:    nil,
			kind:     "CustomBackend",
			expected: false,
		},
		{
			name:     "empty group with core kind",
			group:    nil,
			kind:     "Service",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translator.isExtensionBackendKind(tt.group, tt.kind)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProcessDestinationWithCustomBackend(t *testing.T) {
	translator := &Translator{
		GatewayControllerName: egv1a1.GatewayControllerName,
		ExtensionGroupKinds: []egv1a1.GroupVersionKind{
			{
				Group:   "example.extensions.io",
				Version: "v1alpha1",
				Kind:    "CustomBackend",
			},
		},
	}

	// Create a custom backend resource
	customBackend := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "example.extensions.io/v1alpha1",
			"kind":       "CustomBackend",
			"metadata": map[string]interface{}{
				"name":      "s3-backend",
				"namespace": "default",
			},
			"spec": map[string]interface{}{
				"type": "s3",
				"config": map[string]interface{}{
					"endpoints": []interface{}{
						map[string]interface{}{
							"host": "s3.amazonaws.com",
							"port": 443,
						},
					},
				},
			},
		},
	}

	resources := &resource.Resources{
		ExtensionServerPolicies: []unstructured.Unstructured{*customBackend},
	}

	backendRef := &gwapiv1a2.BackendRef{
		BackendObjectReference: gwapiv1.BackendObjectReference{
			Group: ptr.To(gwapiv1.Group("example.extensions.io")),
			Kind:  ptr.To(gwapiv1.Kind("CustomBackend")),
			Name:  "s3-backend",
			Port:  ptr.To(gwapiv1.PortNumber(443)),
		},
		Weight: ptr.To(int32(100)),
	}

	// Create a mock parent ref and route context
	parentRef := &RouteParentContext{}
	httpRoute := &HTTPRouteContext{
		HTTPRoute: &gwapiv1.HTTPRoute{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-route",
				Namespace: "default",
			},
		},
	}

	ds, err := translator.processDestination(
		"test-destination",
		*backendRef,
		parentRef,
		httpRoute,
		resources,
	)

	require.NoError(t, err)
	require.NotNil(t, ds)
	assert.Equal(t, "test-destination", ds.Name)
	assert.True(t, ds.ExtensionBackend)
	assert.Equal(t, ptr.To(uint32(100)), ds.Weight)
}
