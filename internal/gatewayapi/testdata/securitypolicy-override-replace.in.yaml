gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: listener-1
      protocol: HTTP
      port: 80
      hostname: listener-1.gateway-1.envoyproxy.io
      allowedRoutes:
        namespaces:
          from: All
    - name: listener-2
      protocol: HTTP
      port: 80
      hostname: listener-2.gateway-1.envoyproxy.io
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: listener-1
    rules:
    - matches:
      - path:
          value: "/foo"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-2
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: listener-1
    rules:
    - matches:
      - path:
          value: "/bar"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-3
  spec:
    hostnames:
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: listener-2
    rules:
    - matches:
      - path:
          value: "/baz"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-4
  spec:
    hostnames:
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: listener-2
    rules:
    - matches:
      - path:
          value: "/httproute-4"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-5
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
    rules:
    - matches:
      - path:
          value: "/httproute-5"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-6
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
    rules:
    - matches:
      - path:
          value: "/httproute-6"
      backendRefs:
      - name: service-1
        port: 8080
securityPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    namespace: envoy-gateway
    name: policy-for-gateway-1
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    cors:
      allowOrigins:
      - "http://*.example.com"
      - "http://foo.bar.com"
      - "https://*"
      allowMethods:
      - GET
      - POST
      allowHeaders:
      - "x-header-1"
      - "x-header-2"
      exposeHeaders:
      - "x-header-3"
      - "x-header-4"
      maxAge: 1000s
    jwt:
      providers:
      - name: example1
        issuer: https://one.example.com
        audiences:
        - one.foo.com
        remoteJWKS:
          uri: https://one.example.com/jwt/public-key/jwks.json
        claimToHeaders:
        - header: one-route-example-key
          claim: claim1
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    namespace: envoy-gateway
    name: policy-for-listener-2
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
      sectionName: listener-2
    cors:
      allowHeaders:
      - "x-listener-2"
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    namespace: default
    name: policy-for-route-1
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    cors:
      allowOrigins:
      - "https://*.test.com:8080"
      - "https://www.test.org:8080"
      allowMethods:
      - GET
      - POST
      allowHeaders:
      - "x-header-5"
      - "x-header-6"
      exposeHeaders:
      - "x-header-7"
      - "x-header-8"
      maxAge: 2000s
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    namespace: default
    name: policy-for-route-3
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-3
    cors:
      allowOrigins:
      - "http://*.example.com"
      maxAge: 1000s
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    namespace: default
    name: policy-for-route-5
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-5
    cors:
      allowHeaders:
      - "x-httproute-5"
