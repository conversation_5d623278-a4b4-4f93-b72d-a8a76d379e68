gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      hostname: listener-1.gateway-1.envoyproxy.io
      name: listener-1
      port: 80
      protocol: HTTP
    - allowedRoutes:
        namespaces:
          from: All
      hostname: listener-2.gateway-1.envoyproxy.io
      name: listener-2
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 4
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: listener-1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
    - attachedRoutes: 4
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: listener-2
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: listener-1
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /foo
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-1
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: default
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: listener-1
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /bar
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-1
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-3
    namespace: default
  spec:
    hostnames:
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: listener-2
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /baz
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-2
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-4
    namespace: default
  spec:
    hostnames:
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: listener-2
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /httproute-4
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-2
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-5
    namespace: default
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /httproute-5
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-6
    namespace: default
  spec:
    hostnames:
    - listener-1.gateway-1.envoyproxy.io
    - listener-2.gateway-1.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /httproute-6
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/listener-1
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
securityPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-route-1
    namespace: default
  spec:
    cors:
      allowHeaders:
      - x-header-5
      - x-header-6
      allowMethods:
      - GET
      - POST
      allowOrigins:
      - https://*.test.com:8080
      - https://www.test.org:8080
      exposeHeaders:
      - x-header-7
      - x-header-8
      maxAge: 33m20s
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-1
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-route-3
    namespace: default
  spec:
    cors:
      allowOrigins:
      - http://*.example.com
      maxAge: 16m40s
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-3
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-2
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-route-5
    namespace: default
  spec:
    cors:
      allowHeaders:
      - x-httproute-5
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-5
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-listener-2
    namespace: envoy-gateway
  spec:
    cors:
      allowHeaders:
      - x-listener-2
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
      sectionName: listener-2
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-2
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'This policy is being overridden by other securityPolicies for these
          routes: [default/httproute-3 default/httproute-5]'
        reason: Overridden
        status: "True"
        type: Overridden
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-gateway-1
    namespace: envoy-gateway
  spec:
    cors:
      allowHeaders:
      - x-header-1
      - x-header-2
      allowMethods:
      - GET
      - POST
      allowOrigins:
      - http://*.example.com
      - http://foo.bar.com
      - https://*
      exposeHeaders:
      - x-header-3
      - x-header-4
      maxAge: 16m40s
    jwt:
      providers:
      - audiences:
        - one.foo.com
        claimToHeaders:
        - claim: claim1
          header: one-route-example-key
        issuer: https://one.example.com
        name: example1
        remoteJWKS:
          uri: https://one.example.com/jwt/public-key/jwks.json
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'This policy is being overridden by other securityPolicies for these
          listeners: [listener-2] and these routes: [default/httproute-1 default/httproute-3
          default/httproute-5]'
        reason: Overridden
        status: "True"
        type: Overridden
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - listener-1.gateway-1.envoyproxy.io
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-1
      name: envoy-gateway/gateway-1/listener-1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-5
            namespace: default
          name: httproute/default/httproute-5/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-5/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-1.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-5
          namespace: default
        name: httproute/default/httproute-5/rule/0/match/0/listener-1_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /httproute-5
        security:
          cors:
            allowHeaders:
            - x-httproute-5
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-6
            namespace: default
          name: httproute/default/httproute-6/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-6/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-1.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-6
          namespace: default
        name: httproute/default/httproute-6/rule/0/match/0/listener-1_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /httproute-6
        security:
          cors:
            allowHeaders:
            - x-header-1
            - x-header-2
            allowMethods:
            - GET
            - POST
            allowOrigins:
            - distinct: false
              name: ""
              safeRegex: http://.*\.example\.com
            - distinct: false
              exact: http://foo.bar.com
              name: ""
            - distinct: false
              name: ""
              safeRegex: https://.*
            exposeHeaders:
            - x-header-3
            - x-header-4
            maxAge: 16m40s
          jwt:
            providers:
            - audiences:
              - one.foo.com
              claimToHeaders:
              - claim: claim1
                header: one-route-example-key
              issuer: https://one.example.com
              name: example1
              remoteJWKS:
                uri: https://one.example.com/jwt/public-key/jwks.json
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-1.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/listener-1_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo
        security:
          cors:
            allowHeaders:
            - x-header-5
            - x-header-6
            allowMethods:
            - GET
            - POST
            allowOrigins:
            - distinct: false
              name: ""
              safeRegex: https://.*\.test\.com:8080
            - distinct: false
              exact: https://www.test.org:8080
              name: ""
            exposeHeaders:
            - x-header-7
            - x-header-8
            maxAge: 33m20s
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: default
          name: httproute/default/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-1.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: default
        name: httproute/default/httproute-2/rule/0/match/0/listener-1_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
        security:
          cors:
            allowHeaders:
            - x-header-1
            - x-header-2
            allowMethods:
            - GET
            - POST
            allowOrigins:
            - distinct: false
              name: ""
              safeRegex: http://.*\.example\.com
            - distinct: false
              exact: http://foo.bar.com
              name: ""
            - distinct: false
              name: ""
              safeRegex: https://.*
            exposeHeaders:
            - x-header-3
            - x-header-4
            maxAge: 16m40s
          jwt:
            providers:
            - audiences:
              - one.foo.com
              claimToHeaders:
              - claim: claim1
                header: one-route-example-key
              issuer: https://one.example.com
              name: example1
              remoteJWKS:
                uri: https://one.example.com/jwt/public-key/jwks.json
    - address: 0.0.0.0
      hostnames:
      - listener-2.gateway-1.envoyproxy.io
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: listener-2
      name: envoy-gateway/gateway-1/listener-2
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-4
            namespace: default
          name: httproute/default/httproute-4/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-4/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-2.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-4
          namespace: default
        name: httproute/default/httproute-4/rule/0/match/0/listener-2_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /httproute-4
        security:
          cors:
            allowHeaders:
            - x-listener-2
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-5
            namespace: default
          name: httproute/default/httproute-5/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-5/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-2.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-5
          namespace: default
        name: httproute/default/httproute-5/rule/0/match/0/listener-2_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /httproute-5
        security:
          cors:
            allowHeaders:
            - x-httproute-5
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-6
            namespace: default
          name: httproute/default/httproute-6/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-6/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-2.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-6
          namespace: default
        name: httproute/default/httproute-6/rule/0/match/0/listener-2_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /httproute-6
        security:
          cors:
            allowHeaders:
            - x-listener-2
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-3
            namespace: default
          name: httproute/default/httproute-3/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-3/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: listener-2.gateway-1.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-3
          namespace: default
        name: httproute/default/httproute-3/rule/0/match/0/listener-2_gateway-1_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /baz
        security:
          cors:
            allowOrigins:
            - distinct: false
              name: ""
              safeRegex: http://.*\.example\.com
            maxAge: 16m40s
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
